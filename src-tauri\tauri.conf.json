{"$schema": "https://beta.tauri.app/schemas/config.json", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist"}, "package": {"productName": "<PERSON> Shuffler", "version": "0.1.0"}, "tauri": {"bundle": {"active": true, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.lightning-shuffler.dev", "targets": "all"}, "security": {"csp": null}, "windows": [{"fullscreen": false, "height": 800, "resizable": true, "title": "<PERSON> Shuffler", "width": 1200, "minWidth": 900, "minHeight": 600}]}, "plugins": {"shell": {"open": true}}}