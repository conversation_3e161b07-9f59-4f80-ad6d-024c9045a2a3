import { useEffect } from 'react';
import { useYouTubeStore } from '../store/youtubeStore';

export function useKeyboardShortcuts() {
  const { 
    togglePlay, 
    playNext, 
    playPrevious,
    playerRef 
  } = useYouTubeStore();
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ignore if typing in an input field
      if (
        document.activeElement instanceof HTMLInputElement ||
        document.activeElement instanceof HTMLTextAreaElement
      ) {
        return;
      }
      
      switch (e.key) {
        case ' ':
        case 'k':
          e.preventDefault();
          togglePlay();
          break;
          
        case 'ArrowRight':
          if (playerRef) {
            e.preventDefault();
            const currentTime = playerRef.getCurrentTime();
            playerRef.seekTo(currentTime + 10, true);
          }
          break;
          
        case 'ArrowLeft':
          if (playerRef) {
            e.preventDefault();
            const currentTime = playerRef.getCurrentTime();
            playerRef.seekTo(Math.max(currentTime - 10, 0), true);
          }
          break;
          
        case 'm':
          if (playerRef) {
            e.preventDefault();
            const isMuted = playerRef.isMuted();
            if (isMuted) {
              playerRef.unMute();
            } else {
              playerRef.mute();
            }
          }
          break;
          
        case 'h':
          e.preventDefault();
          // TODO: Show keyboard shortcuts help
          console.log('Show keyboard shortcuts help');
          break;
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [togglePlay, playNext, playPrevious, playerRef]);
}