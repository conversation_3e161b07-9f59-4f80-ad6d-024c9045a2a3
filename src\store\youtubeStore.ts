import { create } from 'zustand';
import { fetchPlaylistData } from '../services/youtubeApi';
import type { Playlist, Video } from '../types/youtube';

interface YouTubeState {
  playlists: Playlist[];
  currentPlaylist: Playlist | null;
  currentVideo: Video | null;
  isPlaying: boolean;
  playerRef: any;
  
  // Actions
  setPlaylists: (playlists: Playlist[]) => void;
  setCurrentPlaylist: (playlist: Playlist) => void;
  setCurrentVideo: (video: Video) => void;
  setPlaying: (playing: boolean) => void;
  setPlayerRef: (ref: any) => void;
  
  // Thunks
  fetchPlaylist: (playlistId: string) => Promise<void>;
  playVideo: (video: Video) => void;
  togglePlay: () => void;
  playNext: () => void;
  playPrevious: () => void;
}

export const useYouTubeStore = create<YouTubeState>((set, get) => ({
  playlists: [],
  currentPlaylist: null,
  currentVideo: null,
  isPlaying: false,
  playerRef: null,
  
  // Actions
  setPlaylists: (playlists) => set({ playlists }),
  setCurrentPlaylist: (playlist) => set({ currentPlaylist: playlist }),
  setCurrentVideo: (video) => set({ currentVideo: video }),
  setPlaying: (playing) => set({ isPlaying: playing }),
  setPlayerRef: (ref) => set({ playerRef: ref }),
  
  // Thunks
  fetchPlaylist: async (playlistId) => {
    try {
      const playlist = await fetchPlaylistData(playlistId);
      
      const { playlists } = get();
      const updatedPlaylists = [...playlists];
      
      // Check if playlist already exists
      const existingIndex = playlists.findIndex(p => p.id === playlist.id);
      
      if (existingIndex >= 0) {
        // Update existing playlist
        updatedPlaylists[existingIndex] = playlist;
      } else {
        // Add new playlist
        updatedPlaylists.push(playlist);
      }
      
      set({ 
        playlists: updatedPlaylists,
        currentPlaylist: playlist,
        currentVideo: playlist.videos[0] || null
      });
    } catch (error) {
      console.error('Failed to fetch playlist:', error);
      throw error;
    }
  },
  
  playVideo: (video) => {
    set({ currentVideo: video, isPlaying: true });
    // If player reference exists, play the video
    const { playerRef } = get();
    if (playerRef) {
      setTimeout(() => {
        playerRef.playVideo();
      }, 0);
    }
  },
  
  togglePlay: () => {
    const { isPlaying, playerRef } = get();
    
    if (!playerRef) return;
    
    if (isPlaying) {
      playerRef.pauseVideo();
    } else {
      playerRef.playVideo();
    }
    
    set({ isPlaying: !isPlaying });
  },
  
  playNext: () => {
    const { currentPlaylist, currentVideo } = get();
    
    if (!currentPlaylist || !currentVideo) return;
    
    const currentIndex = currentPlaylist.videos.findIndex(v => v.id === currentVideo.id);
    
    if (currentIndex < currentPlaylist.videos.length - 1) {
      const nextVideo = currentPlaylist.videos[currentIndex + 1];
      get().playVideo(nextVideo);
    }
  },
  
  playPrevious: () => {
    const { currentPlaylist, currentVideo } = get();
    
    if (!currentPlaylist || !currentVideo) return;
    
    const currentIndex = currentPlaylist.videos.findIndex(v => v.id === currentVideo.id);
    
    if (currentIndex > 0) {
      const prevVideo = currentPlaylist.videos[currentIndex - 1];
      get().playVideo(prevVideo);
    }
  }
}));