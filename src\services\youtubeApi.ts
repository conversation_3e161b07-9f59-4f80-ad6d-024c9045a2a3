import type { Playlist, Video } from '../types/youtube';

// TODO: Replace with your actual YouTube API key from the vision document
const API_KEY = 'AIzaSyAFGB8-5IffhA-sAtvt7MYQJLwQJZTPypI';
const BASE_URL = 'https://www.googleapis.com/youtube/v3';

/**
 * Fetches a YouTube playlist and its videos
 */
export async function fetchPlaylistData(playlistId: string): Promise<Playlist> {
  try {
    // 1. Fetch playlist details
    const playlistResponse = await fetch(
      `${BASE_URL}/playlists?part=snippet&id=${playlistId}&key=${API_KEY}`
    );
    
    if (!playlistResponse.ok) {
      throw new Error('Failed to fetch playlist information');
    }
    
    const playlistData = await playlistResponse.json();
    
    if (!playlistData.items || playlistData.items.length === 0) {
      throw new Error('Playlist not found');
    }
    
    const playlistDetails = playlistData.items[0];
    
    // 2. Fetch playlist items (videos)
    const videos = await fetchPlaylistVideos(playlistId);
    
    return {
      id: playlistId,
      title: playlistDetails.snippet.title,
      description: playlistDetails.snippet.description,
      thumbnail: playlistDetails.snippet.thumbnails.high?.url || playlistDetails.snippet.thumbnails.default?.url,
      channelTitle: playlistDetails.snippet.channelTitle,
      videos
    };
  } catch (error) {
    console.error('Error fetching playlist:', error);
    throw error;
  }
}

/**
 * Fetches all videos in a playlist
 */
async function fetchPlaylistVideos(playlistId: string): Promise<Video[]> {
  const videos: Video[] = [];
  let nextPageToken: string | undefined = undefined;
  
  do {
    const url = `${BASE_URL}/playlistItems?part=snippet,contentDetails&maxResults=50&playlistId=${playlistId}&key=${API_KEY}${
      nextPageToken ? `&pageToken=${nextPageToken}` : ''
    }`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error('Failed to fetch playlist videos');
    }
    
    const data = await response.json();
    
    // Get video IDs to fetch additional details
    const videoIds = data.items
      .map((item: any) => item.contentDetails.videoId)
      .join(',');
    
    // Fetch video details to get duration
    const videoDetailsResponse = await fetch(
      `${BASE_URL}/videos?part=contentDetails,statistics&id=${videoIds}&key=${API_KEY}`
    );
    
    if (!videoDetailsResponse.ok) {
      throw new Error('Failed to fetch video details');
    }
    
    const videoDetails = await videoDetailsResponse.json();
    
    // Map video details to our format
    for (const item of data.items) {
      // Find the corresponding video details
      const details = videoDetails.items.find(
        (v: any) => v.id === item.contentDetails.videoId
      );
      
      if (item.snippet.title !== 'Private video' && item.snippet.title !== 'Deleted video') {
        videos.push({
          id: item.contentDetails.videoId,
          title: item.snippet.title,
          description: item.snippet.description,
          thumbnail: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default?.url,
          channelTitle: item.snippet.channelTitle,
          duration: details?.contentDetails.duration || 'PT0S',
          viewCount: details?.statistics.viewCount || '0'
        });
      }
    }
    
    nextPageToken = data.nextPageToken;
  } while (nextPageToken);
  
  return videos;
}