/**
 * Formats an ISO 8601 duration string to a human-readable format
 * @param isoDuration ISO 8601 duration string (e.g., "PT1H30M15S")
 * @returns Formatted duration string (e.g., "1:30:15")
 */
export function formatDuration(isoDuration: string): string {
  // Regular expression to extract hours, minutes, and seconds
  const match = isoDuration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  
  if (!match) return '0:00';
  
  const hours = match[1] ? parseInt(match[1], 10) : 0;
  const minutes = match[2] ? parseInt(match[2], 10) : 0;
  const seconds = match[3] ? parseInt(match[3], 10) : 0;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

/**
 * Formats a view count to a more readable format
 * @param viewCount View count as a string
 * @returns Formatted view count (e.g., "1.2M")
 */
export function formatViewCount(viewCount: string): string {
  const count = parseInt(viewCount, 10);
  
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  } else {
    return count.toString();
  }
}