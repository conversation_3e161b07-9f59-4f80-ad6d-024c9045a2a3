import React, { useState } from 'react';
import { Zap } from 'lucide-react';
import AppLayout from './components/layouts/AppLayout';
import { YouTubeProvider } from './contexts/YouTubeContext';

function App() {
  return (
    <YouTubeProvider>
      <div className="flex flex-col h-screen w-screen overflow-hidden bg-[var(--background)]">
        <header className="flex items-center px-6 py-3 bg-[var(--surface)] border-b border-gray-800">
          <Zap size={24} className="text-[var(--primary)] mr-3 lightning-animation" />
          <h1 className="text-xl font-bold text-white">Lightning Shuffler</h1>
        </header>
        
        <AppLayout />
      </div>
    </YouTubeProvider>
  );
}

export default App;