import React, { createContext, useContext, ReactNode } from 'react';
import { useYouTubeStore } from '../store/youtubeStore';

interface YouTubeContextType {
  currentVideo: any;
  isPlaying: boolean;
  currentPlaylist: any;
  togglePlay: () => void;
  playNext: () => void;
  playPrevious: () => void;
  playVideo: (video: any) => void;
  fetchPlaylist: (playlistId: string) => Promise<void>;
}

const YouTubeContext = createContext<YouTubeContextType | undefined>(undefined);

export const YouTubeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const store = useYouTubeStore();
  
  return (
    <YouTubeContext.Provider value={store}>
      {children}
    </YouTubeContext.Provider>
  );
};

export const useYouTube = () => {
  const context = useContext(YouTubeContext);
  if (context === undefined) {
    throw new Error('useYouTube must be used within a YouTubeProvider');
  }
  return context;
};