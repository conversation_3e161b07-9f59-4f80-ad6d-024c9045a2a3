import React, { useState } from 'react';
import { 
  Play, 
  Pause, 
  SkipBack, 
  Ski<PERSON>Forward, 
  Repeat, 
  Volume2, 
  VolumeX,
  Shuffle
} from 'lucide-react';
import { useYouTubeStore } from '../../store/youtubeStore';

const PlayerControls: React.FC = () => {
  const { 
    isPlaying, 
    togglePlay, 
    playNext, 
    playPrevious,
    playerRef
  } = useYouTubeStore();
  
  const [isMuted, setIsMuted] = useState(false);
  const [loopCount, setLoopCount] = useState(0);
  
  const toggleMute = () => {
    if (!playerRef) return;
    
    if (isMuted) {
      playerRef.unMute();
    } else {
      playerRef.mute();
    }
    
    setIsMuted(!isMuted);
  };
  
  const toggleLoop = (e: React.MouseEvent) => {
    if (e.shiftKey || e.button === 2) {
      // Increment loop count (shift+click or right click)
      setLoopCount(prev => (prev + 1) % 10);
    } else {
      // Toggle loop on/off (normal click)
      setLoopCount(prev => (prev === 0 ? 1 : 0));
    }
  };
  
  const handleShuffle = () => {
    // TODO: Implement shuffle functionality
    console.log('Shuffle playlist');
  };

  return (
    <div className="glass-panel p-4 flex items-center justify-between">
      <button 
        className="p-2 hover:text-[var(--primary)] transition-colors"
        onClick={toggleMute}
      >
        {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
      </button>
      
      <div className="flex items-center space-x-6">
        <button 
          className="p-2 hover:text-[var(--primary)] transition-colors"
          onClick={playPrevious}
        >
          <SkipBack size={24} />
        </button>
        
        <button 
          className="p-3 bg-[var(--primary)] text-black rounded-full hover:bg-[var(--primary-dim)] transition-colors"
          onClick={togglePlay}
        >
          {isPlaying ? <Pause size={24} /> : <Play size={24} />}
        </button>
        
        <button 
          className="p-2 hover:text-[var(--primary)] transition-colors"
          onClick={playNext}
        >
          <SkipForward size={24} />
        </button>
      </div>
      
      <div className="flex items-center space-x-4">
        <button 
          className={`p-2 relative transition-colors ${loopCount > 0 ? 'text-[var(--primary)]' : 'hover:text-[var(--primary)]'}`}
          onClick={toggleLoop}
          onContextMenu={(e) => {
            e.preventDefault();
            toggleLoop(e);
          }}
        >
          <Repeat size={20} />
          {loopCount > 1 && (
            <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs font-bold">
              {loopCount}
            </span>
          )}
        </button>
        
        <button 
          className="p-2 hover:text-[var(--primary)] transition-colors"
          onClick={handleShuffle}
        >
          <Shuffle size={20} />
        </button>
      </div>
    </div>
  );
};

export default PlayerControls;