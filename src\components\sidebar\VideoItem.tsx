import React from 'react';
import { useYouTubeStore } from '../../store/youtubeStore';
import { Play } from 'lucide-react';
import type { Video } from '../../types/youtube';
import { formatDuration, formatViewCount } from '../../utils/formatters';

interface VideoItemProps {
  video: Video;
  isActive: boolean;
}

const VideoItem: React.FC<VideoItemProps> = ({ video, isActive }) => {
  const { playVideo } = useYouTubeStore();

  return (
    <div 
      className={`
        flex items-start p-3 cursor-pointer hover:bg-[var(--surface-light)] transition-all duration-300
        ${isActive ? 'bg-[var(--surface-light)] border-l-2 border-[var(--primary)]' : ''}
      `}
      onClick={() => playVideo(video)}
    >
      <div className={`
        relative flex-shrink-0 w-24 h-16 mr-3 rounded overflow-hidden
        ${isActive ? 'glow-animation' : ''}
      `}>
        <img 
          src={video.thumbnail} 
          alt={video.title} 
          className="w-full h-full object-cover"
        />
        {isActive && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
            <Play size={24} className="text-[var(--primary)]" fill="currentColor" />
          </div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <h4 className={`text-sm font-medium truncate ${isActive ? 'text-[var(--primary)]' : 'text-white'}`}>
          {video.title}
        </h4>
        <p className="text-xs text-gray-400 mt-1 truncate">
          {video.channelTitle}
        </p>
        <div className="flex items-center text-xs text-gray-500 mt-0.5 space-x-2">
          <span>{formatDuration(video.duration)}</span>
          <span>•</span>
          <span>{formatViewCount(video.viewCount)} views</span>
        </div>
      </div>
    </div>
  );
};

export default VideoItem;