A beautiful looking, nice to use,*performant and snappy* playlist shuffler (Windows 11) app called Lightning shuffler, with the following general vision/ideas/features/plan for it:

1. Overall Aesthetic & Branding
Dark Mode with Neon Green Accents:

The app embraces a dark mode interface as its default. The primary accent color is a vibrant neon green, reminiscent of a lightning bolt—infusing energy into buttons, highlights, and key UI elements.
Subtle gradients shadow effects, rounded corners, blurs, glassmorphism, and other modern trends keep the app aligned with the other modern sites and apps of today's internet.
Incorporate lightning-inspired motifs (such as a stylized lightning bolt logo - an included one has been made for you named Lightning_Bolt.png, with the application icon (for the taskbar, etc.) being called App_Icon.png) throughout the app, especially in the splash screen and loading animations.
(Everything should be visually consistent as well)

Smooth, Modern Animations:
Transitions between screens that keep the app feeling responsive and alive.
Micro-interactions provide immediate feedback, making the app feel responsive and polished.
Animations for loading states (e.g., refreshing a playlist) should be minimalistic yet engaging—a spinning neon-green icon or a subtle “shuffling” animation that echoes the app’s theme.
2. Playlist & Mix Management
Adding & Managing Playlists:

Users can easily add new YouTube playlists by entering a URL. When a valid URL is detected, the playlist is automatically fetched and added to the manager. (To get data from Youtube, you'll need an API key. Luckily, we already have one: AIzaSyAFGB8-5IffhA-sAtvt7MYQJLwQJZTPypI)
Persistent storage of what playlists the user has added (should be stored on users computer, so no servers or databases will be needed)
Management Options: Each playlist displays with a thumbnail, title, and video count. Options for deleting or refreshing are available with buttons with clear and understandable icons. When the refresh button is clicked, an animated indicator shows that the app is fetching updated data from YouTube. Of course, there should also be a confirmation dialog before deleting a playlist.

Combining Playlists: Users can create “Mixes” by selecting multiple playlists to combine into a custom, named collection.
Mix Interface: The mix is treated like any other playlist but is visually distinguished (e.g., stack of playlists, or whatever else looks best) to signal its combined nature.
Mix Management: Similar management options (rename, delete, and refresh - which should refresh all playlists in the mix) should be available for mixes, with an intuitive selection interface to add or remove individual playlists from a mix.
Additionally, users can create mixes by typing in a comma separated list of playlist URLs, which will be helpful for the sharing functionality listed later.
Search & Filter Capabilities:

A prominently placed search bar above the sidebar lets users filter through all videos by title or author.
The search experience is dynamic—typing immediately narrows down the list with subtle animations and highlights matching text within video entries. It should be able to search through both the title of videos and their authors.
3. Video Playback & Controls
Main Video Display:

The central area of the app is reserved for video playback. The player should be clean and modern, with rounded corners and a gradient based off of general video color around it, making the video seem to flow along with the rest of the app (this should be a toggleable setting).
A subtle glow around the video in the sidebar that's also animated when the next video in the playlist automatically begins playing
Control Section (Below the Video):

Play, Previous, Next, and Reshuffle: A control bar features distinct, well-spaced icons for play/pause, previous, next, and reshuffle button.
The reshuffle button triggers an animated “deck of cards” effect on the sidebar, visually rearranging the video order.
Loop Button with Advanced Interaction (also in control bar):
A single-click toggles on or off any loop function.
Shift-Left Click or Right-Click: Each such click increases a visible loop counter displayed in the icon itself (a number appears centrally on the loop icon - make sure the icon we use has a big enough open space in the center for this number to fit, while adjusting the other icons in the control bar to make sure they don't look out of place). After each completed loop, the counter decreases until it eventually returns to a default off state.
The loop icon should have a dynamic feel — its state changes and number transitions are smoothly animated to emphasize responsiveness.
Dynamic Tab Title:

The app should additionally be able to be minimized to the system tray, and hovering over it should show the title of the currently playing video, and the percentage of it that has already played, something like this: "0% | Video Title". Clicking it should result in something similar to the mockup availible as "system tray controls.png"

The app integrates with system-level media controls so that dedicated hardware keys (or system shortcuts) for next/previous track work seamlessly even when the app is minimized or another window is active, and so users with earbuds can control the app using the on-board controls.
The integration includes displaying the current video’s title, thumbnail, and progress in the system media overlay.

4. Sidebar & Navigation
Scrollable Sidebar (Queue View):

The sidebar displays a list of all videos queued to play along with a history of those that have already played. Simply scroll up to see what played earlier, and scroll down to see what's next, all to the very last video in the whole playlist.
The sidebar is scrollable, and positioned above it is the search bar, which is designed to be responsive. When engaged, it expands smoothly and provides real-time filtering, allowing users to search videos by name or author.
The search results are highlighted (differently than the currently playing video to avoid confusion) and, if no match is found, an elegant “No results” message appears with suggestions (e.g., “Check your spelling or try a different keyword”).

5. Additional User Experience Enhancements
Keyboard Shortcuts & Accessibility:
Spacebar or k for play/pause
Left and right arrows for going forward and backward 10 seconds in the video
M for mute/unmute
“H” to open a quick-help overlay listing all shortcuts (especially important for the loop functionality, so this should be shown when a new user first uses the app).