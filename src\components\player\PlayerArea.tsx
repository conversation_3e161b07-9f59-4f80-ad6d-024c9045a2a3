import React, { useEffect, useState } from 'react';
import ColorThief from 'colorthief';
import { Zap, Plus } from 'lucide-react';
import VideoPlayer from './VideoPlayer';
import PlayerControls from './PlayerControls';
import { useYouTubeStore } from '../../store/youtubeStore';

const PlayerArea: React.FC = () => {
  const { currentVideo, currentPlaylist } = useYouTubeStore();
  const [gradientColors, setGradientColors] = useState<string[]>(['#121212', '#1e1e1e']);

  useEffect(() => {
    if (currentVideo?.thumbnail) {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.src = currentVideo.thumbnail;
      
      img.onload = () => {
        const colorThief = new ColorThief();
        const palette = colorThief.getPalette(img, 2);
        
        if (palette) {
          const colors = palette.map(([r, g, b]) => `rgb(${r}, ${g}, ${b})`);
          setGradientColors(colors);
        }
      };
    }
  }, [currentVideo]);

  const gradientStyle = {
    background: `radial-gradient(circle at center, ${gradientColors[0]}00 0%, ${gradientColors[1]}66 100%)`,
  };

  return (
    <div className="flex flex-col h-full p-6">
      <div className="flex-1 flex items-center justify-center min-h-0 relative">
        <div className="absolute inset-0" style={gradientStyle} />
        {currentVideo ? (
          <div className="glass-panel w-full max-w-4xl p-4 aspect-video relative z-10">
            <VideoPlayer />
          </div>
        ) : (
          <div className="glass-panel w-full max-w-3xl p-8 text-center relative z-10">
            <div className="text-[var(--primary)] mb-4">
              <Zap size={48} className="mx-auto lightning-animation" />
            </div>
            <h2 className="text-2xl font-bold mb-3">Welcome to Lightning Shuffler</h2>
            <p className="text-gray-400 mb-6">
              Add a YouTube playlist to start watching videos with lightning speed
            </p>
            <button 
              onClick={() => {/* TODO: Open add playlist modal */}}
              className="neon-btn mx-auto"
            >
              <Plus size={18} className="mr-2 inline-block" />
              Add Your First Playlist
            </button>
          </div>
        )}
      </div>
      
      {currentVideo && (
        <div className="mt-6 relative z-10">
          <h2 className="text-xl font-bold mb-1">{currentVideo.title}</h2>
          <p className="text-gray-400 mb-4">{currentVideo.channelTitle}</p>
          <PlayerControls />
        </div>
      )}
    </div>
  );
};

export default PlayerArea;