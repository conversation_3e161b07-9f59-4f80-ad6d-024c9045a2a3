import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useYouTubeStore } from '../../store/youtubeStore';

interface AddPlaylistProps {
  onClose: () => void;
}

const AddPlaylist: React.FC<AddPlaylistProps> = ({ onClose }) => {
  const [playlistUrl, setPlaylistUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { fetchPlaylist } = useYouTubeStore();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!playlistUrl) {
      setError('Please enter a playlist URL');
      return;
    }
    
    // Extract playlist ID from URL
    const playlistId = extractPlaylistId(playlistUrl);
    
    if (!playlistId) {
      setError('Invalid playlist URL. Please enter a valid YouTube playlist URL.');
      return;
    }
    
    setIsLoading(true);
    
    try {
      await fetchPlaylist(playlistId);
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to fetch playlist');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Function to extract playlist ID from various YouTube URL formats
  function extractPlaylistId(url: string): string | null {
    const regex = /[?&]list=([^&]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-75 z-50">
      <div className="glass-panel w-full max-w-md p-6 animate-fadeIn">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold">Add YouTube Playlist</h3>
          <button 
            onClick={onClose}
            className="p-1 hover:text-[var(--primary)] transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="playlistUrl" className="block text-sm font-medium mb-1">
              Playlist URL
            </label>
            <input
              id="playlistUrl"
              type="text"
              value={playlistUrl}
              onChange={(e) => setPlaylistUrl(e.target.value)}
              placeholder="https://www.youtube.com/playlist?list=..."
              className="w-full bg-[var(--surface-light)] p-3 rounded-md text-white border border-gray-700 focus:border-[var(--primary)] focus:outline-none transition-all"
            />
            {error && (
              <p className="text-red-400 text-sm mt-1">{error}</p>
            )}
          </div>
          
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-3 px-4 py-2 text-gray-300 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`neon-btn ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {isLoading ? 'Loading...' : 'Add Playlist'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddPlaylist;