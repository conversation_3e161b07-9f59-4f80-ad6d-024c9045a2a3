@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #39FF14;
  --primary-dim: #32d912;
  --background: #121212;
  --surface: #1e1e1e;
  --surface-light: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
}

@layer base {
  html {
    @apply bg-black text-white;
  }

  body {
    @apply m-0 p-0 overflow-hidden;
    background-color: var(--background);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--surface);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--surface-light);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dim);
  }
}

@layer components {
  .neon-btn {
    @apply bg-transparent text-[var(--primary)] border border-[var(--primary)] rounded-md px-4 py-2 transition-all duration-300;
    box-shadow: 0 0 5px var(--primary), inset 0 0 5px var(--primary);
  }
  
  .neon-btn:hover {
    @apply bg-[var(--primary)] text-black;
    box-shadow: 0 0 15px var(--primary), inset 0 0 5px var(--primary);
  }

  .glass-panel {
    @apply bg-[var(--surface)] bg-opacity-70 backdrop-blur-md rounded-xl;
  }
}

.lightning-animation {
  animation: lightning-pulse 3s infinite;
}

@keyframes lightning-pulse {
  0% {
    filter: drop-shadow(0 0 2px var(--primary));
  }
  50% {
    filter: drop-shadow(0 0 8px var(--primary));
  }
  100% {
    filter: drop-shadow(0 0 2px var(--primary));
  }
}

.glow-animation {
  animation: glow-pulse 2s infinite;
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 5px var(--primary);
  }
  50% {
    box-shadow: 0 0 15px var(--primary);
  }
  100% {
    box-shadow: 0 0 5px var(--primary);
  }
}