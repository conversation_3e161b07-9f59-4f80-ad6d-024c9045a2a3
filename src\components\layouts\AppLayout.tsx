import React, { useState } from 'react';
import Sidebar from '../sidebar/Sidebar';
import PlayerArea from '../player/PlayerArea';
import AddPlaylist from '../playlist/AddPlaylist';

const AppLayout: React.FC = () => {
  const [showAddPlaylist, setShowAddPlaylist] = useState(false);

  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Sidebar with playlist and queue */}
      <Sidebar onAddPlaylist={() => setShowAddPlaylist(true)} />
      
      {/* Main content area with video player */}
      <main className="flex-1 overflow-hidden">
        <PlayerArea />
      </main>

      {/* Add playlist modal */}
      {showAddPlaylist && (
        <AddPlaylist onClose={() => setShowAddPlaylist(false)} />
      )}
    </div>
  );
};

export default AppLayout;