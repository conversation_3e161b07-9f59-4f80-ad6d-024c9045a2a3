import React, { useState } from 'react';
import { Search, Plus, Shuffle, Music } from 'lucide-react';
import { useYouTubeStore } from '../../store/youtubeStore';
import VideoItem from './VideoItem';

interface SidebarProps {
  onAddPlaylist: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onAddPlaylist }) => {
  const { currentPlaylist, currentVideo } = useYouTubeStore();
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredVideos = currentPlaylist?.videos.filter(video => 
    video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    video.channelTitle.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  return (
    <aside className="w-80 flex flex-col border-r border-gray-800 bg-[var(--surface)]">
      <div className="p-4 border-b border-gray-800">
        <button 
          onClick={onAddPlaylist}
          className="flex items-center justify-center w-full p-2 neon-btn mb-4"
        >
          <Plus size={18} className="mr-2" />
          <span>Add Playlist</span>
        </button>
        
        <div className="relative">
          <input
            type="text"
            placeholder="Search videos..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-[var(--surface-light)] p-2 pl-9 rounded-md text-white border border-gray-700 focus:border-[var(--primary)] focus:outline-none transition-all"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={16} />
        </div>
      </div>
      
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        <div className="flex items-center">
          <Music size={16} className="mr-2 text-[var(--primary)]" />
          <h3 className="font-medium">{currentPlaylist?.title || 'No Playlist'}</h3>
        </div>
        <button className="p-1.5 hover:bg-[var(--surface-light)] rounded-md transition-colors">
          <Shuffle size={16} className="text-[var(--primary)]" />
        </button>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {filteredVideos.length > 0 ? (
          <div className="py-2">
            {filteredVideos.map((video) => (
              <VideoItem
                key={video.id}
                video={video}
                isActive={currentVideo?.id === video.id}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-400">
            {searchTerm ? (
              <>
                <p>No videos found</p>
                <p className="text-sm mt-1">Try a different search term</p>
              </>
            ) : (
              <>
                <Zap size={32} className="mb-2 text-[var(--primary)]" />
                <p>No videos available</p>
                <p className="text-sm mt-1">Add a playlist to get started</p>
              </>
            )}
          </div>
        )}
      </div>
    </aside>
  );
};

export default Sidebar;