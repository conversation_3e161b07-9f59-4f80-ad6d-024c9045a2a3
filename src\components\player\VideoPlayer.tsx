import React, { useEffect, useState } from 'react';
import YouTube from 'react-youtube';
import { useYouTubeStore } from '../../store/youtubeStore';

const VideoPlayer: React.FC = () => {
  const { 
    currentVideo, 
    isPlaying, 
    setPlaying, 
    setPlayerRef 
  } = useYouTubeStore();
  
  const [playerHeight, setPlayerHeight] = useState('480');
  
  useEffect(() => {
    const updateDimensions = () => {
      const containerWidth = document.querySelector('.glass-panel')?.clientWidth || 640;
      // 16:9 aspect ratio
      const height = Math.floor(containerWidth * 9 / 16);
      setPlayerHeight(height.toString());
    };
    
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);
  
  const onPlayerReady = (event: any) => {
    setPlayerRef(event.target);
    // Auto-play when ready
    if (isPlaying) {
      event.target.playVideo();
    }
  };
  
  const onPlayerStateChange = (event: any) => {
    // Handle player state changes
    // 1 = playing, 2 = paused
    setPlaying(event.data === 1);
    
    // Handle video ended (state = 0)
    if (event.data === 0) {
      // TODO: Play next video in queue
    }
  };

  if (!currentVideo) return null;
  
  return (
    <div className="w-full h-full">
      <YouTube
        videoId={currentVideo.id}
        opts={{
          width: '100%',
          height: playerHeight,
          playerVars: {
            autoplay: isPlaying ? 1 : 0,
            controls: 1,
            modestbranding: 1,
            rel: 0
          },
        }}
        onReady={onPlayerReady}
        onStateChange={onPlayerStateChange}
        className="w-full h-full"
      />
    </div>
  );
};

export default VideoPlayer;